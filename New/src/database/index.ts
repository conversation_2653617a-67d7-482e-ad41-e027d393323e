import { neon } from "@neondatabase/serverless";
import { getConfig } from "@utils/configs";
import { drizzle } from "drizzle-orm/neon-serverless";
import dbSchema from "./schema";

/**
 * Drizzle ORM client instance, connected to Neon Postgres.
 *
 * Uses the serverless neon driver without connection pooling,
 * which is optimal for Cloudflare Workers stateless runtime.
 * Each request creates a new connection that is automatically
 * cleaned up when the request completes.
 *
 * @see https://orm.drizzle.team/docs/overview
 */
export const getDb = () => {
	const sql = neon(getConfig("databaseUrl") as string);
	return drizzle(sql, { schema: dbSchema });
};

export { dbSchema };
