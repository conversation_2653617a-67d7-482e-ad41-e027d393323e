/**
 * Comprehensive Application Configuration Interface for DermaCare Sync Service
 *
 * This interface defines all configuration values used throughout the application,
 * providing a centralized location for managing settings, timeouts, thresholds,
 * and other configurable parameters.
 *
 * **Configuration Categories:**
 * - Database connection settings
 * - API endpoints and authentication
 * - Cache and performance settings
 * - Buffer and retry configurations
 * - Performance thresholds and limits
 * - Error logging settings
 * - Webhook processing timeouts
 *
 * @since 1.0.0
 * @version 2.0.0
 */
interface AppConfigs {
	// Database Configuration
	databaseUrl: string;

	// API Configuration
	ccApiDomain: string;
	ccApiKey: string;
	apApiKey: string;
	apApiDomain: string;
	locationID: string;
	apCalendarId: string;

	// Basic Cache and Request Configuration
	cacheTTL: number; // Default cache TTL in seconds
	maxRetries: number; // Maximum retry attempts
	requestTimeout: number; // Request timeout in milliseconds
	syncBufferTimeSec: number; // Buffer time in seconds to prevent unnecessary syncs

	// Performance Thresholds (in milliseconds)
	performance: {
		totalRequestTimeout: number; // Total webhook processing timeout
		databaseQueryTimeout: number; // Database query timeout
		apiCallTimeout: number; // API call timeout
		memoryLimit: number; // Memory limit in bytes
		warningThreshold: number; // Warning threshold percentage (0-1)
		criticalThreshold: number; // Critical threshold percentage (0-1)
		batchOperationTimeout: number; // Batch operation timeout
		cacheOperationTimeout: number; // Cache operation timeout
	};

	// Cache TTL Configuration (in milliseconds)
	cacheTTLs: {
		patientData: number; // Patient data cache TTL
		appointmentData: number; // Appointment data cache TTL
		customFields: number; // Custom fields cache TTL
		apiResponses: number; // API response cache TTL
		locations: number; // Location data cache TTL
		defaultCloudflare: number; // Default Cloudflare cache TTL
		maxMemoryEntries: number; // Maximum memory cache entries per request
	};

	// Circuit Breaker Configuration
	circuitBreaker: {
		failureThreshold: number; // Number of failures before opening circuit
		recoveryTimeout: number; // Recovery timeout in milliseconds
		monitoringPeriod: number; // Monitoring period in milliseconds
	};

	// Concurrent Request Limits
	concurrency: {
		ccApiMaxConcurrent: number; // Max concurrent CC API requests
		apApiMaxConcurrent: number; // Max concurrent AP API requests
	};

	// Retry Configuration
	retry: {
		maxAttempts: number; // Maximum retry attempts
		baseDelay: number; // Base delay in milliseconds
		maxDelay: number; // Maximum delay in milliseconds
		timeoutMs: number; // Total retry timeout in milliseconds
	};

	// Error Logging Configuration
	errorLogging: {
		duplicateWindowSeconds: number; // Duplicate error prevention window
		enableDatabaseLogging: boolean; // Enable database error logging
		enableConsoleLogging: boolean; // Enable console error logging
	};

	// Optimization Configuration
	optimization: {
		enableParallel: boolean; // Enable parallel processing
		maxParallel: number; // Maximum parallel operations
		enableCaching: boolean; // Enable caching
		enableBatching: boolean; // Enable batch operations
		operationTimeout: number; // Operation timeout in milliseconds
		memoryLimit: number; // Memory limit in bytes
	};
}

/**
 * Centralized configuration object containing all application settings
 *
 * **Configuration Management:**
 * All configuration values are centralized in this file. To modify settings,
 * update the values directly in this configuration object.
 *
 * **Performance Optimization:**
 * All timeout and threshold values are optimized for the 25-second webhook
 * completion requirement while maintaining reliability and user experience.
 */
const configs: AppConfigs = {
	// Database Configuration
	databaseUrl:
		"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",

	// API Configuration
	ccApiDomain: "https://ccdemo.clinicore.eu/api/v1",
	ccApiKey:
		"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
	apApiDomain: "https://services.leadconnectorhq.com",
	apApiKey: "pit-19f7d314-acb6-45d7-bae7-2a56ccf789b6",
	locationID: "CIY0QcIvP7m9TxVWlvy3",
	apCalendarId: "ZsyisrZNAGBZsLAE60zj",

	// Basic Configuration
	cacheTTL: 300, // 5 minutes (default cache TTL in seconds)
	maxRetries: 3, // Maximum retry attempts
	requestTimeout: 30000, // 30 seconds (request timeout in milliseconds)
	syncBufferTimeSec: 60, // 1 minute buffer time to prevent unnecessary syncs

	// Performance Thresholds (optimized for 25-second webhook completion)
	performance: {
		totalRequestTimeout: 25000, // 25 seconds - Cloudflare Workers timeout
		databaseQueryTimeout: 1500, // 1.5 seconds - Reduced for better performance
		apiCallTimeout: 8000, // 8 seconds - Reduced to allow for retries
		memoryLimit: 128 * 1024 * 1024, // 128MB
		warningThreshold: 0.75, // 75% of limit - Earlier warning
		criticalThreshold: 0.9, // 90% of limit - Critical alert
		batchOperationTimeout: 5000, // 5 seconds for batch operations
		cacheOperationTimeout: 100, // 100ms for cache operations
	},

	// Cache TTL Configuration (in milliseconds)
	cacheTTLs: {
		patientData: 10 * 60 * 1000, // 10 minutes for patient data
		appointmentData: 5 * 60 * 1000, // 5 minutes for appointment data
		customFields: 30 * 60 * 1000, // 30 minutes for custom field configs
		apiResponses: 2 * 60 * 1000, // 2 minutes for API responses
		locations: 30 * 60 * 1000, // 30 minutes for location data
		defaultCloudflare: 5 * 60 * 1000, // 5 minutes default Cloudflare cache
		maxMemoryEntries: 100, // Maximum memory cache entries per request
	},

	// Circuit Breaker Configuration
	circuitBreaker: {
		failureThreshold: 5, // Number of failures before opening circuit
		recoveryTimeout: 30000, // 30 seconds recovery timeout
		monitoringPeriod: 60000, // 1 minute monitoring period
	},

	// Concurrent Request Limits
	concurrency: {
		ccApiMaxConcurrent: 5, // Max concurrent CC API requests
		apApiMaxConcurrent: 3, // Max concurrent AP API requests
	},

	// Retry Configuration
	retry: {
		maxAttempts: 3, // Maximum retry attempts
		baseDelay: 1000, // 1 second base delay
		maxDelay: 5000, // 5 seconds maximum delay
		timeoutMs: 20000, // 20 seconds total retry timeout (leave 5s buffer)
	},

	// Error Logging Configuration
	errorLogging: {
		duplicateWindowSeconds: 30, // Prevent duplicates within 30 seconds
		enableDatabaseLogging: true, // Enable database error logging
		enableConsoleLogging: true, // Enable console error logging
	},

	// Optimization Configuration
	optimization: {
		enableParallel: true, // Enable parallel processing
		maxParallel: 3, // Maximum parallel operations
		enableCaching: true, // Enable caching
		enableBatching: true, // Enable batch operations
		operationTimeout: 20000, // 20 seconds operation timeout
		memoryLimit: 100 * 1024 * 1024, // 100MB memory limit
	},
};

/**
 * Get a specific top-level configuration value
 * @param key - Configuration key to retrieve
 * @returns The configuration value
 */
export const getConfig = (key: keyof AppConfigs): AppConfigs[typeof key] => {
	const v = configs[key] as AppConfigs[typeof key];
	if (v === undefined || v === null) {
		console.error(`Config ${key} is not defined.`);
		throw new Error(`Config ${key} is not defined.`);
	}
	return v;
};

/**
 * Get performance threshold configuration
 * @param key - Performance threshold key
 * @returns Performance threshold value
 */
export const getPerformanceConfig = (
	key: keyof AppConfigs["performance"],
): number => {
	const value = configs.performance[key];
	if (value === undefined || value === null) {
		console.error(`Performance config ${key} is not defined.`);
		throw new Error(`Performance config ${key} is not defined.`);
	}
	return value;
};

/**
 * Get cache TTL configuration
 * @param key - Cache TTL key
 * @returns Cache TTL value in milliseconds
 */
export const getCacheTTL = (key: keyof AppConfigs["cacheTTLs"]): number => {
	const value = configs.cacheTTLs[key];
	if (value === undefined || value === null) {
		console.error(`Cache TTL config ${key} is not defined.`);
		throw new Error(`Cache TTL config ${key} is not defined.`);
	}
	return value;
};

/**
 * Get circuit breaker configuration
 * @param key - Circuit breaker key
 * @returns Circuit breaker configuration value
 */
export const getCircuitBreakerConfig = (
	key: keyof AppConfigs["circuitBreaker"],
): number => {
	const value = configs.circuitBreaker[key];
	if (value === undefined || value === null) {
		console.error(`Circuit breaker config ${key} is not defined.`);
		throw new Error(`Circuit breaker config ${key} is not defined.`);
	}
	return value;
};

/**
 * Get concurrency configuration
 * @param key - Concurrency key
 * @returns Concurrency limit value
 */
export const getConcurrencyConfig = (
	key: keyof AppConfigs["concurrency"],
): number => {
	const value = configs.concurrency[key];
	if (value === undefined || value === null) {
		console.error(`Concurrency config ${key} is not defined.`);
		throw new Error(`Concurrency config ${key} is not defined.`);
	}
	return value;
};

/**
 * Get retry configuration
 * @param key - Retry configuration key
 * @returns Retry configuration value
 */
export const getRetryConfig = (key: keyof AppConfigs["retry"]): number => {
	const value = configs.retry[key];
	if (value === undefined || value === null) {
		console.error(`Retry config ${key} is not defined.`);
		throw new Error(`Retry config ${key} is not defined.`);
	}
	return value;
};

/**
 * Get error logging configuration
 * @param key - Error logging configuration key
 * @returns Error logging configuration value
 */
export const getErrorLoggingConfig = (
	key: keyof AppConfigs["errorLogging"],
): boolean | number => {
	const value = configs.errorLogging[key];
	if (value === undefined || value === null) {
		console.error(`Error logging config ${key} is not defined.`);
		throw new Error(`Error logging config ${key} is not defined.`);
	}
	return value;
};

/**
 * Get optimization configuration
 * @param key - Optimization configuration key
 * @returns Optimization configuration value
 */
export const getOptimizationConfig = (
	key: keyof AppConfigs["optimization"],
): boolean | number => {
	const value = configs.optimization[key];
	if (value === undefined || value === null) {
		console.error(`Optimization config ${key} is not defined.`);
		throw new Error(`Optimization config ${key} is not defined.`);
	}
	return value;
};

/**
 * Get all configuration values
 * @returns Complete configuration object
 */
const getConfigs = (): AppConfigs => {
	return configs;
};

/**
 * Validate configuration on startup
 * @throws Error if any required configuration is missing or invalid
 */
export const validateConfig = (): void => {
	const requiredKeys: (keyof AppConfigs)[] = [
		"databaseUrl",
		"ccApiDomain",
		"ccApiKey",
		"apApiDomain",
		"apApiKey",
		"locationID",
		"apCalendarId",
	];

	for (const key of requiredKeys) {
		const value = configs[key];
		if (!value || (typeof value === "string" && value.trim() === "")) {
			throw new Error(`Required configuration ${key} is missing or empty`);
		}
	}

	// Validate performance thresholds
	if (configs.performance.totalRequestTimeout <= 0) {
		throw new Error("Performance totalRequestTimeout must be greater than 0");
	}

	// Validate cache TTLs
	if (configs.cacheTTLs.defaultCloudflare <= 0) {
		throw new Error("Cache TTL defaultCloudflare must be greater than 0");
	}

	console.log("✅ Configuration validation passed");
};

export default getConfig;
export { getConfigs };
export type { AppConfigs };
