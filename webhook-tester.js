#!/usr/bin/env node

/**
 * DermaCare Webhook Testing Utility
 *
 * Comprehensive testing tool for all webhook endpoints in the DermaCare bi-directional sync system.
 * Tests CC webhooks, AP webhooks, error scenarios, and edge cases.
 *
 * **Key Features:**
 * - Creates real records in CC and AP systems using their respective APIs
 * - Tests all webhook endpoints with legitimate data from source systems
 * - Uses actual CC and AP record IDs returned from API calls in webhook payloads
 * - Supports multiple environments and test suites
 * - Comprehensive error scenario testing
 *
 * **API Integration:**
 * - Creates test patients in CliniCore using CC API
 * - Creates test contacts in AutoPatient using AP API
 * - Creates test appointments in both systems using their respective APIs
 * - Uses real IDs from API responses in webhook payloads
 * - Cleans up test data from both systems after completion
 *
 * Usage:
 *   node webhook-tester.js [environment] [test-suite] [--cleanup]
 *
 * Examples:
 *   node webhook-tester.js local all
 *   node webhook-tester.js staging cc-webhooks
 *   node webhook-tester.js production ap-webhooks --cleanup
 */

// Import required modules
const fetch = require('node-fetch');

// API configuration - matches configs.ts from the main application
const API_CONFIG = {
  cc: {
    domain: "https://ccdemo.clinicore.eu/api/v1",
    apiKey: "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************.e2lamZ2zIJ5Ho5jBRPKsJYbL0LmIMCQPoWnUuwbdg1EUju0sq4JygNRYd2BsYbgE3NGJe-NgGQTRkTS1HLgSan7gkshhfD7-6Mi05GgPQ5meOEWVHCrs-Cpgx45b16fXz8qZR4TrXxcy0LBBZHomHf-oMuTcPeeGswr_RzPtW2KAISAyrSUJ2gR1ru1Y9k3nq0e4V0Dj7AL_qnThxbfMbjIdqw80Mt1LHuBrtL-nw7AJKSQLp7UwMziQ2bT7D0y77arpEin1mkjTCIoZTXGCScxet8dnAKp88aZgmKlDn8v0wB0Mt9f7o3TaXokY7kp4I_dgIeO7PitygizYClUslHy7QJl1SsRK39ByqQY_HDkVxE3cFfXmSn88KcNOGlilRYkewqYfUAvRLgYaeHb2uwS-_dRr93zkhMQOzUMr-JZvgmjsYpR3Bo1zCoridKNuTHm63RYYU0n-C5iMnY8ZQNYLtzvV0dZiIhDezxpCNhREbTe12xCOyCUhOSwq9nILgYLtyPV0_v7Nup_oYTodbD41rqixhZNQz1JMjdUIUFgHF4gPycnnU5UACPRul18u8CDMUgy3oGcHGY9Ax4Q41witMQ2XrkCMfYIksjS8YXH5sukCAHzbyN5WfF1fqgWa78_n78sWXpFzsrYaTs98W3b18NhqHVHbZmISRLSEBhE"
  },
  ap: {
    domain: "https://services.leadconnectorhq.com",
    apiKey: "pit-19f7d314-acb6-45d7-bae7-2a56ccf789b6",
    locationId: "CIY0QcIvP7m9TxVWlvy3",
    calendarId: "ZsyisrZNAGBZsLAE60zj"
  }
};

// Global variables for test data cleanup
let createdTestData = {
  ccPatients: [],
  ccAppointments: [],
  apContacts: [],
  apAppointments: []
};

// Configuration for different environments
const ENVIRONMENTS = {
  local: {
    baseUrl: 'http://localhost:8787',
    name: 'Local Development'
  },
  staging: {
    baseUrl: 'https://staging-dermacare-sync.example.com',
    name: 'Staging Environment'
  },
  production: {
    baseUrl: 'https://dermacare-sync.example.com',
    name: 'Production Environment'
  },
  ngrok: {
    baseUrl: 'https://proven-moose-hopefully.ngrok-free.app',
    name: 'Ngrok Tunnel'
  }
};

// API helper functions
async function makeApiRequest(url, options = {}) {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return response.json();
}

async function createTestCCPatient(patientData = {}) {
  const testEmail = patientData.email || `test-${Date.now()}@example.com`;
  const testPhone = patientData.phone || `+1${Math.floor(Math.random() * **********) + **********}`;

  const ccPatientData = {
    firstName: patientData.firstName || 'Test',
    lastName: patientData.lastName || 'Patient',
    email: testEmail,
    phoneMobile: testPhone,
    dob: patientData.dob || '1990-01-01',
    gender: patientData.gender || 'male'
  };

  console.log(`   Creating CC patient: ${ccPatientData.firstName} ${ccPatientData.lastName} (${ccPatientData.email})`);

  const response = await makeApiRequest(`${API_CONFIG.cc.domain}/patients`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_CONFIG.cc.apiKey}`
    },
    body: JSON.stringify({ patient: ccPatientData })
  });

  const createdPatient = response.patient;

  // Track for cleanup
  createdTestData.ccPatients.push(createdPatient.id);

  console.log(`   ✅ Created CC patient with ID: ${createdPatient.id}`);
  return createdPatient;
}

async function createTestAPContact(contactData = {}) {
  const testEmail = contactData.email || `test-${Date.now()}@example.com`;
  const testPhone = contactData.phone || `+1${Math.floor(Math.random() * **********) + **********}`;

  const apContactData = {
    firstName: contactData.firstName || 'Test',
    lastName: contactData.lastName || 'Contact',
    email: testEmail,
    phone: testPhone,
    locationId: API_CONFIG.ap.locationId,
    tags: ['webhook-test']
  };

  console.log(`   Creating AP contact: ${apContactData.firstName} ${apContactData.lastName} (${apContactData.email})`);

  const response = await makeApiRequest(`${API_CONFIG.ap.domain}/contacts/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_CONFIG.ap.apiKey}`,
      'Version': '2021-04-15'
    },
    body: JSON.stringify(apContactData)
  });

  const createdContact = response.contact;

  // Track for cleanup
  createdTestData.apContacts.push(createdContact.id);

  console.log(`   ✅ Created AP contact with ID: ${createdContact.id}`);
  return createdContact;
}

async function createTestCCAppointment(ccPatientId, appointmentData = {}) {
  const startsAt = appointmentData.startsAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
  const endsAt = appointmentData.endsAt || new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString();

  const ccAppointmentData = {
    title: appointmentData.title || 'Test Appointment',
    startsAt: startsAt,
    endsAt: endsAt,
    patients: [ccPatientId]
  };

  console.log(`   Creating CC appointment: ${ccAppointmentData.title} for patient ${ccPatientId}`);

  const response = await makeApiRequest(`${API_CONFIG.cc.domain}/appointments`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_CONFIG.cc.apiKey}`
    },
    body: JSON.stringify({ appointment: ccAppointmentData })
  });

  const createdAppointment = response.appointment;

  // Track for cleanup
  createdTestData.ccAppointments.push(createdAppointment.id);

  console.log(`   ✅ Created CC appointment with ID: ${createdAppointment.id}`);
  return createdAppointment;
}

async function createTestAPAppointment(apContactId, appointmentData = {}) {
  const startsAt = appointmentData.startsAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
  const endsAt = appointmentData.endsAt || new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString();

  const apAppointmentData = {
    calendarId: API_CONFIG.ap.calendarId,
    contactId: apContactId,
    startTime: startsAt,
    endTime: endsAt,
    title: appointmentData.title || 'Test Appointment',
    appointmentStatus: 'confirmed'
  };

  console.log(`   Creating AP appointment: ${apAppointmentData.title} for contact ${apContactId}`);

  const response = await makeApiRequest(`${API_CONFIG.ap.domain}/calendars/events/appointments`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_CONFIG.ap.apiKey}`,
      'Version': '2021-04-15'
    },
    body: JSON.stringify(apAppointmentData)
  });

  const createdAppointment = response.appointment;

  // Track for cleanup
  createdTestData.apAppointments.push(createdAppointment.id);

  console.log(`   ✅ Created AP appointment with ID: ${createdAppointment.id}`);
  return createdAppointment;
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data from CC and AP systems...');

  try {
    // Delete AP appointments first
    if (createdTestData.apAppointments.length > 0) {
      console.log(`   Deleting ${createdTestData.apAppointments.length} AP appointments...`);
      for (const appointmentId of createdTestData.apAppointments) {
        try {
          await makeApiRequest(`${API_CONFIG.ap.domain}/calendars/events/appointments/${appointmentId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${API_CONFIG.ap.apiKey}`,
              'Version': '2021-04-15'
            }
          });
          console.log(`     ✅ Deleted AP appointment: ${appointmentId}`);
        } catch (error) {
          console.log(`     ⚠️  Failed to delete AP appointment ${appointmentId}: ${error.message}`);
        }
      }
    }

    // Delete CC appointments
    if (createdTestData.ccAppointments.length > 0) {
      console.log(`   Deleting ${createdTestData.ccAppointments.length} CC appointments...`);
      for (const appointmentId of createdTestData.ccAppointments) {
        try {
          await makeApiRequest(`${API_CONFIG.cc.domain}/appointments/${appointmentId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${API_CONFIG.cc.apiKey}`
            }
          });
          console.log(`     ✅ Deleted CC appointment: ${appointmentId}`);
        } catch (error) {
          console.log(`     ⚠️  Failed to delete CC appointment ${appointmentId}: ${error.message}`);
        }
      }
    }

    // Delete AP contacts
    if (createdTestData.apContacts.length > 0) {
      console.log(`   Deleting ${createdTestData.apContacts.length} AP contacts...`);
      for (const contactId of createdTestData.apContacts) {
        try {
          await makeApiRequest(`${API_CONFIG.ap.domain}/contacts/${contactId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${API_CONFIG.ap.apiKey}`,
              'Version': '2021-04-15'
            }
          });
          console.log(`     ✅ Deleted AP contact: ${contactId}`);
        } catch (error) {
          console.log(`     ⚠️  Failed to delete AP contact ${contactId}: ${error.message}`);
        }
      }
    }

    // Delete CC patients
    if (createdTestData.ccPatients.length > 0) {
      console.log(`   Deleting ${createdTestData.ccPatients.length} CC patients...`);
      for (const patientId of createdTestData.ccPatients) {
        try {
          await makeApiRequest(`${API_CONFIG.cc.domain}/patients/${patientId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${API_CONFIG.cc.apiKey}`
            }
          });
          console.log(`     ✅ Deleted CC patient: ${patientId}`);
        } catch (error) {
          console.log(`     ⚠️  Failed to delete CC patient ${patientId}: ${error.message}`);
        }
      }
    }

    // Reset tracking arrays
    createdTestData = {
      ccPatients: [],
      ccAppointments: [],
      apContacts: [],
      apAppointments: []
    };

    console.log('✅ Test data cleanup completed successfully');
  } catch (error) {
    console.error('❌ Error during test data cleanup:', error.message);
  }
}

// Test data generation functions (using real CC and AP APIs)
async function generateTestData() {
  console.log('📊 Creating test records in CC and AP systems...');

  // Create test patient in CC
  const ccPatient = await createTestCCPatient({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneMobile: "+**********",
    dob: "1990-01-01",
    gender: "male"
  });

  // Create test contact in AP
  const apContact = await createTestAPContact({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+**********"
  });

  // Create test appointment in CC
  const ccAppointment = await createTestCCAppointment(ccPatient.id, {
    title: "Test Appointment",
    startsAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    endsAt: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString()
  });

  // Create test appointment in AP
  const apAppointment = await createTestAPAppointment(apContact.id, {
    title: "Test Appointment",
    startsAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    endsAt: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString()
  });

  console.log(`   ✅ Created test patient with CC ID: ${ccPatient.id}`);
  console.log(`   ✅ Created test contact with AP ID: ${apContact.id}`);
  console.log(`   ✅ Created test appointment with CC ID: ${ccAppointment.id}`);
  console.log(`   ✅ Created test appointment with AP ID: ${apAppointment.id}`);

  return {
    ccPatient: {
      event: "EntityWasCreated",
      model: "Patient",
      id: ccPatient.id,
      payload: ccPatient
    },

    ccAppointment: {
      event: "AppointmentWasCreated",
      model: "Appointment",
      id: ccAppointment.id,
      payload: ccAppointment
    },

    apContact: {
      event: "ContactCreated",
      model: "Contact",
      id: apContact.id,
      payload: apContact
    },

    apAppointment: {
      event: "AppointmentCreated",
      model: "Appointment",
      id: apAppointment.id,
      payload: apAppointment
    },

    ccInvoice: {
      event: "EntityWasCreated",
      model: "Invoice",
      id: Math.floor(Math.random() * 1000000) + 100000,
      payload: {
        id: Math.floor(Math.random() * 1000000) + 100000,
        patient: ccPatient.id,
        amount: 150.00,
        status: "paid",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        pdfUrl: "https://example.com/invoice.pdf"
      }
    },

    ccPayment: {
      event: "EntityWasCreated",
      model: "Payment",
      id: Math.floor(Math.random() * 1000000) + 100000,
      payload: {
        id: Math.floor(Math.random() * 1000000) + 100000,
        patient: ccPatient.id,
        amount: 150.00,
        status: "completed",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        pdfUrl: "https://example.com/payment.pdf"
      }
    },

    apContact: {
      event: "contact.created",
      data: {
        contact: {
          id: `ap_contact_${Date.now()}`,
          firstName: "Jane",
          lastName: "Smith",
          email: `jane.smith.${Date.now()}@example.com`,
          phone: `+1${Math.floor(Math.random() * **********) + **********}`,
          dateAdded: new Date().toISOString(),
          dateUpdated: new Date().toISOString(),
          tags: ["test"],
          customFields: []
        }
      }
    },

    apAppointment: {
      event: "appointment.created",
      data: {
        calendar: {
          appointmentId: `ap_appointment_${Date.now()}`,
          startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
          title: "AP Test Appointment",
          created_by_meta: {
            source: "user"
          },
          last_updated_by_meta: {
            source: "user"
          }
        },
        contact_id: `ap_contact_${Date.now()}`
      }
    }
  };
}

// Test suites generator (uses real test data)
function generateTestSuites(testData) {
  return {
    'cc-webhooks': [
      { name: 'CC Patient Created', endpoint: '/webhook', data: testData.ccPatient },
      { name: 'CC Patient Updated', endpoint: '/webhook', data: { ...testData.ccPatient, event: "EntityWasUpdated" } },
      { name: 'CC Appointment Created', endpoint: '/webhook', data: testData.ccAppointment },
      { name: 'CC Appointment Updated', endpoint: '/webhook', data: { ...testData.ccAppointment, event: "EntityWasUpdated" } },
      { name: 'CC Invoice Created', endpoint: '/webhook', data: testData.ccInvoice },
      { name: 'CC Payment Created', endpoint: '/webhook', data: testData.ccPayment }
    ],

    'ap-webhooks': [
      { name: 'AP Contact Created', endpoint: '/ap/contact', data: testData.apContact },
      { name: 'AP Contact Updated', endpoint: '/ap/contact', data: { ...testData.apContact, event: "contact.updated" } },
      { name: 'AP Appointment Created', endpoint: '/ap/appointment', data: testData.apAppointment },
      { name: 'AP Appointment Updated', endpoint: '/ap/appointment', data: { ...testData.apAppointment, event: "appointment.updated" } }
    ],

    'error-scenarios': [
      { name: 'Invalid JSON', endpoint: '/webhook', data: 'invalid-json', expectError: true },
      { name: 'Missing Event Type', endpoint: '/webhook', data: { model: "Patient", id: 123 }, expectError: true },
      { name: 'Unknown Model', endpoint: '/webhook', data: { event: "EntityWasCreated", model: "UnknownModel", id: 123 }, expectError: true },
      { name: 'Missing Payload', endpoint: '/webhook', data: { event: "EntityWasCreated", model: "Patient", id: 123 }, expectError: true }
    ],

    'edge-cases': [
      { name: 'Third Party AP Appointment', endpoint: '/ap/appointment', data: {
        ...testData.apAppointment,
        data: {
          ...testData.apAppointment.data,
          calendar: {
            ...testData.apAppointment.data.calendar,
            created_by_meta: { source: "third_party" }
          }
        }
      }},
      { name: 'Empty Email and Phone', endpoint: '/webhook', data: {
        ...testData.ccPatient,
        payload: { ...testData.ccPatient.payload, email: "", phoneMobile: "" }
      }},
      { name: 'Duplicate Processing', endpoint: '/webhook', data: testData.ccPatient }
    ],

    'health-check': [
      { name: 'Health Check', endpoint: '/health', method: 'GET' }
    ]
  };
}

// Utility functions
async function makeRequest(baseUrl, endpoint, data = null, method = 'POST') {
  const url = `${baseUrl}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'DermaCare-Webhook-Tester/1.0'
    }
  };
  
  if (data && method !== 'GET') {
    options.body = typeof data === 'string' ? data : JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let parsedData;
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = responseData;
    }
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: parsedData,
      headers: Object.fromEntries(response.headers.entries())
    };
  } catch (error) {
    return {
      error: error.message,
      status: 0
    };
  }
}

function formatResult(test, result) {
  const status = result.status;
  const isSuccess = result.expectError ? (status >= 400) : (status >= 200 && status < 300);
  const icon = isSuccess ? '✅' : '❌';
  
  console.log(`${icon} ${test.name}`);
  console.log(`   Status: ${status} ${result.statusText || ''}`);
  
  if (result.error) {
    console.log(`   Error: ${result.error}`);
  } else if (result.data) {
    if (typeof result.data === 'object') {
      console.log(`   Response: ${JSON.stringify(result.data, null, 2).substring(0, 200)}...`);
    } else {
      console.log(`   Response: ${result.data.substring(0, 200)}...`);
    }
  }
  
  console.log('');
  return isSuccess;
}

async function runTestSuite(environment, suiteName, tests) {
  console.log(`\n🧪 Running ${suiteName} tests on ${environment.name}`);
  console.log(`📍 Base URL: ${environment.baseUrl}`);
  console.log('─'.repeat(60));
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    const method = test.method || 'POST';
    const result = await makeRequest(environment.baseUrl, test.endpoint, test.data, method);
    result.expectError = test.expectError;
    
    const success = formatResult(test, result);
    if (success) passed++;
    
    // Add delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`📊 Results: ${passed}/${total} tests passed`);
  return { passed, total };
}

async function main() {
  const args = process.argv.slice(2);
  const envName = args[0] || 'local';
  const suiteNames = args[1] ? [args[1]] : ['all'];
  
  if (!ENVIRONMENTS[envName]) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.error(`Available environments: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    process.exit(1);
  }
  
  const environment = ENVIRONMENTS[envName];
  
  console.log('🚀 DermaCare Webhook Testing Utility');
  console.log(`🌍 Environment: ${environment.name}`);
  console.log(`🔗 Base URL: ${environment.baseUrl}`);
  
  let totalPassed = 0;
  let totalTests = 0;
  
  const suitesToRun = suiteNames.includes('all') ? Object.keys(TEST_SUITES) : suiteNames;
  
  for (const suiteName of suitesToRun) {
    if (!TEST_SUITES[suiteName]) {
      console.error(`❌ Unknown test suite: ${suiteName}`);
      console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}, all`);
      continue;
    }
    
    const result = await runTestSuite(environment, suiteName, TEST_SUITES[suiteName]);
    totalPassed += result.passed;
    totalTests += result.total;
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 Overall Results: ${totalPassed}/${totalTests} tests passed`);
  
  if (totalPassed === totalTests) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please review the results above.');
    process.exit(1);
  }
}

// Note: Stress tests removed for simplicity in this implementation
// They can be added back later if needed with proper test data generation

// Performance monitoring
function measurePerformance(testName, startTime) {
  const endTime = Date.now();
  const duration = endTime - startTime;

  if (duration > 25000) {
    console.log(`⚠️  ${testName} took ${duration}ms (exceeds 25s limit)`);
  } else if (duration > 10000) {
    console.log(`🟡 ${testName} took ${duration}ms (slow but acceptable)`);
  } else {
    console.log(`🟢 ${testName} took ${duration}ms (good performance)`);
  }

  return duration;
}

// Enhanced test runner with performance monitoring
async function runEnhancedTestSuite(environment, suiteName, tests) {
  console.log(`\n🧪 Running ${suiteName} tests on ${environment.name}`);
  console.log(`📍 Base URL: ${environment.baseUrl}`);
  console.log('─'.repeat(60));

  let passed = 0;
  let total = tests.length;
  const performanceData = [];

  for (const test of tests) {
    const startTime = Date.now();
    const method = test.method || 'POST';
    const result = await makeRequest(environment.baseUrl, test.endpoint, test.data, method);
    result.expectError = test.expectError;

    const duration = measurePerformance(test.name, startTime);
    performanceData.push({ name: test.name, duration });

    const success = formatResult(test, result);
    if (success) passed++;

    // Add delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Performance summary
  const avgDuration = performanceData.reduce((sum, p) => sum + p.duration, 0) / performanceData.length;
  const maxDuration = Math.max(...performanceData.map(p => p.duration));

  console.log(`📊 Results: ${passed}/${total} tests passed`);
  console.log(`⏱️  Performance: Avg ${avgDuration.toFixed(0)}ms, Max ${maxDuration}ms`);

  return { passed, total, performanceData };
}

// CLI help
function showHelp() {
  console.log(`
🚀 DermaCare Webhook Testing Utility

Usage:
  node webhook-tester.js [environment] [test-suite] [options]

Environments:
  local      - Local development server (http://localhost:8787)
  ngrok      - Ngrok tunnel (https://proven-moose-hopefully.ngrok-free.app)
  staging    - Staging environment
  production - Production environment

Test Suites:
  all           - Run all test suites
  cc-webhooks   - CC (CliniCore) webhook tests
  ap-webhooks   - AP (AutoPatient) webhook tests
  error-scenarios - Error handling tests
  edge-cases    - Edge case scenarios
  health-check  - Health check endpoint
  stress        - Stress and performance tests

Options:
  --help, -h    - Show this help message
  --verbose, -v - Verbose output
  --performance - Include performance monitoring

Examples:
  node webhook-tester.js local all
  node webhook-tester.js ngrok cc-webhooks
  node webhook-tester.js staging ap-webhooks --performance
  node webhook-tester.js production health-check
`);
}

// Enhanced main function with database integration
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const envName = args[0] || 'local';
  const suiteNames = args[1] ? [args[1]] : ['all'];
  const verbose = args.includes('--verbose') || args.includes('-v');
  const performanceMode = args.includes('--performance');
  const shouldCleanup = args.includes('--cleanup');

  if (!ENVIRONMENTS[envName]) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.error(`Available environments: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    process.exit(1);
  }

  const environment = ENVIRONMENTS[envName];

  console.log('🚀 DermaCare Webhook Testing Utility');
  console.log(`🌍 Environment: ${environment.name}`);
  console.log(`🔗 Base URL: ${environment.baseUrl}`);

  // Initialize test data
  let testData = null;
  let TEST_SUITES = null;

  try {
    // Generate test data using real CC and AP APIs
    testData = await generateTestData();
    console.log('✅ Test data generation completed');

    // Generate test suites with real data
    TEST_SUITES = generateTestSuites(testData);

    let totalPassed = 0;
    let totalTests = 0;
    let allPerformanceData = [];

    const suitesToRun = suiteNames.includes('all') ? Object.keys(TEST_SUITES) : suiteNames;

    // Run test suites
    for (const suiteName of suitesToRun.filter(s => s !== 'stress')) {
      if (!TEST_SUITES[suiteName]) {
        console.error(`❌ Unknown test suite: ${suiteName}`);
        console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}, all, stress`);
        continue;
      }

      const testRunner = performanceMode ? runEnhancedTestSuite : runTestSuite;
      const result = await testRunner(environment, suiteName, TEST_SUITES[suiteName]);

      totalPassed += result.passed;
      totalTests += result.total;

      if (result.performanceData) {
        allPerformanceData.push(...result.performanceData);
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log(`🎯 Overall Results: ${totalPassed}/${totalTests} tests passed`);

    if (performanceMode && allPerformanceData.length > 0) {
      const avgDuration = allPerformanceData.reduce((sum, p) => sum + p.duration, 0) / allPerformanceData.length;
      const slowTests = allPerformanceData.filter(p => p.duration > 10000);

      console.log(`⏱️  Overall Performance: Avg ${avgDuration.toFixed(0)}ms`);
      if (slowTests.length > 0) {
        console.log(`🐌 Slow tests (>10s): ${slowTests.map(t => t.name).join(', ')}`);
      }
    }

    if (totalPassed === totalTests) {
      console.log('🎉 All tests passed!');
    } else {
      console.log('⚠️  Some tests failed. Please review the results above.');
    }

  } catch (error) {
    console.error('💥 Error during testing:', error.message);
    if (verbose) {
      console.error(error.stack);
    }
  } finally {
    // Cleanup test data if requested or if there was an error
    if (shouldCleanup || totalPassed !== totalTests) {
      await cleanupTestData();
    }
  }

  // Exit with appropriate code
  process.exit(totalPassed === totalTests ? 0 : 1);
}

// Handle command line execution
if (require.main === module) {
  main().catch(console.error);
}
